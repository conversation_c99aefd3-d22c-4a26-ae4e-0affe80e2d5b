import { PaginatedResponseDto } from '@common/dto/paginated-response.dto';
import { PaginationDto } from '@common/dto/pagination.dto';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';
import { CustomerService } from '@core/customer/customer.service';
import { ProfileValidationService } from '@core/profile/profile.validation.service';
import { StaffService } from '@core/staff/staff.service';
import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager } from 'typeorm';
import { Profile } from './entities/profile.entity';

@Injectable()
export class ProfileService implements EntityServiceStrategy<Profile> {
  constructor(
    private readonly logger: LoggerService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly profileValidation: ProfileValidationService,
    private readonly staffService: StaffService,
    private readonly customerService: CustomerService,
  ) {
    this.logger.setContext(ProfileService.name);
  }

  async create(data: Profile): Promise<Profile> {
    data.status = EntityStatus.INACTIVE;
    await this.profileValidation.validate(data, DatabaseAction.CREATE);
    return await this.entityManager.save(data);
  }

  async update(data: Profile): Promise<Profile> {
    await this.profileValidation.validate(data, DatabaseAction.UPDATE);
    return await this.entityManager.save(data);
  }

  async findByPk(id: number): Promise<Profile | null> {
    return await this.entityManager.findOneBy(Profile, {
      id,
    });
  }

  async findByEmail(email: string) {
    return await this.entityManager.findOneBy(Profile, {
      email,
    });
  }

  async modify(id: number, data: Profile): Promise<Profile> {
    await this.profileValidation.validate(data, DatabaseAction.UPDATE);
    return await this.entityManager.save(data);
  }

  table<R = Profile>(paginationDto: PaginationDto, ...args: Array<string | number>): Promise<PaginatedResponseDto<R>> {
    return Promise.resolve(undefined);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const profile: Profile = await this.entityManager.findOne(Profile, {
          where: { id },
        });
        profile.status = EntityStatus.ACTIVE;
        profile.profileStatus = ProfileStatus.ACTIVE;
        await this.entityManager.save(Profile, profile);
      }),
    );
  }

  async deactivate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const profile: Profile = await this.entityManager.findOne(Profile, {
          where: { id },
        });
        profile.status = EntityStatus.INACTIVE;
        profile.profileStatus = ProfileStatus.DEACTIVATED;
        await this.entityManager.save(Profile, profile);
      }),
    );
  }

  /**
   * Suspend a user account.
   * @param accountId - The ID of the account to suspend.
   */
  async suspendProfile(profileId: number) {
    const existingProfile = await this.isProfileExists(profileId);

    if (!existingProfile) throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));

    if (existingProfile.profileType === ProfileType.STAFF) {
      const existingStaff = await this.staffService.findStaffByProfileId(existingProfile.id);
      existingStaff.status = EntityStatus.INACTIVE;
      existingStaff.profile.status = EntityStatus.INACTIVE;
      existingStaff.profile.profileStatus = ProfileStatus.SUSPENDED;
      await this.staffService.modify(existingStaff.id, existingStaff);
    }

    if (existingProfile.profileType === ProfileType.CLIENT) {
      const existingCustomer = await this.customerService.findCustomerByProfileId(existingProfile.id);
      existingCustomer.status = EntityStatus.INACTIVE;
      existingCustomer.profile.status = EntityStatus.INACTIVE;
      existingCustomer.profile.profileStatus = ProfileStatus.SUSPENDED;
      await this.customerService.modify(existingCustomer.id, existingCustomer);
    }
  }

  /**
   * Ban a user account.
   * @param profileId
   */
  async banProfile(profileId: number) {
    const existingProfile = await this.isProfileExists(profileId);

    if (!existingProfile) throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));

    if (existingProfile.profileType === ProfileType.STAFF) {
      const existingStaff = await this.staffService.findStaffByProfileId(existingProfile.id);
      existingStaff.status = EntityStatus.INACTIVE;
      existingStaff.profile.status = EntityStatus.INACTIVE;
      existingStaff.profile.profileStatus = ProfileStatus.BANNED;
      await this.staffService.modify(existingStaff.id, existingStaff);
    }

    if (existingProfile.profileType === ProfileType.CLIENT) {
      const existingCustomer = await this.customerService.findCustomerByProfileId(existingProfile.id);
      existingCustomer.status = EntityStatus.INACTIVE;
      existingCustomer.profile.status = EntityStatus.INACTIVE;
      existingCustomer.profile.profileStatus = ProfileStatus.BANNED;
      await this.customerService.modify(existingCustomer.id, existingCustomer);
    }
  }

  /**
   * Check if an account exists.
   * @param profileId - The ID of the account to check.
   */
  isProfileExists(profileId: number) {
    return this.entityManager.findOne(Profile, {
      where: { id: profileId },
    });
  }

  async checkProfileEligibility(profile: Profile): Promise<boolean> {
    if (profile.verified === false) {
      throw new ForbiddenException(this.i18n.t('message.errors.not_verified', { args: { entity: 'Profile' } }));
    }

    if (profile.profileStatus === ProfileStatus.SUSPENDED) {
      throw new ForbiddenException(this.i18n.t('message.errors.profile_suspended'));
    }

    if (profile.profileStatus === ProfileStatus.BANNED) {
      throw new ForbiddenException(this.i18n.t('message.errors.profile_banned'));
    }

    if (profile.profileStatus === ProfileStatus.DEACTIVATED) {
      throw new ForbiddenException(this.i18n.t('message.errors.profile_deactivated'));
    }

    return true;
  }

  async markProfileVerified(email: string) {
    const existingProfile = await this.entityManager.findOneBy(Profile, { email });
    if (!existingProfile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));
    }

    if (existingProfile.profileType === ProfileType.STAFF) {
      const existingStaff = await this.staffService.findStaffByProfileId(existingProfile.id);
      existingStaff.status = EntityStatus.ACTIVE;
      existingStaff.profile.status = EntityStatus.ACTIVE;
      existingStaff.profile.profileStatus = ProfileStatus.ACTIVE;
      existingStaff.profile.verified = true;
      await this.staffService.modify(existingStaff.id, existingStaff);
    }

    if (existingProfile.profileType === ProfileType.CLIENT) {
      const existingCustomer = await this.customerService.findCustomerByProfileId(existingProfile.id);
      existingCustomer.status = EntityStatus.ACTIVE;
      existingCustomer.profile.status = EntityStatus.ACTIVE;
      existingCustomer.profile.profileStatus = ProfileStatus.ACTIVE;
      existingCustomer.profile.verified = true;
      await this.customerService.modify(existingCustomer.id, existingCustomer);
    }
  }

  async findProfileByEmail(email: string): Promise<Profile> {
    return await this.entityManager.findOne(Profile, { where: { email } });
  }
}
