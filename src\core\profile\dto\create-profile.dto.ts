import { AutoMap } from '@automapper/classes';
import { Gender } from '@common/enumerations/gender.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { CreateAddressDto } from '@core/address/dto/create-address.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEmail, IsEnum, IsOptional, IsPhoneNumber, IsString, ValidateNested } from 'class-validator';

export class CreateProfileDto {
  @AutoMap()
  @ApiProperty({
    description: 'The first name of the user',
    example: '<PERSON>',
  })
  @IsString()
  firstName: string;

  @AutoMap()
  @ApiProperty({
    description: 'The last name of the user',
    example: 'Doe',
    required: false,
  })
  @IsString()
  @IsOptional()
  lastName?: string;

  @AutoMap()
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @AutoMap()
  @ApiProperty({
    description: 'The phone number of the user',
    example: '+2348123456789',
  })
  @IsPhoneNumber()
  phoneNumber: string;

  @AutoMap()
  @ApiProperty({
    description: 'The password of the user',
    example: 'SecureP@ssw0rd',
  })
  @IsString()
  password: string;

  @AutoMap()
  @ApiProperty({
    description: 'The gender of the user',
    enum: Gender,
  })
  @IsEnum(Gender)
  gender: Gender;

  @AutoMap()
  @ApiProperty({
    description: 'The profile type of the user',
    enum: ProfileType,
  })
  @IsEnum(ProfileType)
  profileType: ProfileType;

  @AutoMap()
  @ApiProperty({
    description: 'The address of the user',
  })
  @ValidateNested()
  @Type(() => CreateAddressDto)
  @IsOptional()
  address: CreateAddressDto;
}
