import { Mapper, MappingProfile, createMap } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { WalletDto } from './dto/wallet.dto';
import { Wallet } from './entities/wallet.entity';

@Injectable()
export class WalletMapperService extends AutomapperProfile {
   constructor(
      @InjectMapper() mapper: Mapper,
      private readonly logger: LoggerService,
    ) {
      super(mapper);
      this.logger.setContext(WalletMapperService.name);
    }
  
    override get profile(): MappingProfile {
      return (mapper) => {
        createMap(mapper, WalletDto, Wallet);
        createMap(mapper, Wallet, WalletDto);
      };
    }
}
