{"errors": {"not_found": "{entity} not found", "already_exists": "{entity} already exists", "ability": "You can't delete {entity}", "login": "<PERSON><PERSON> failed", "signup": "Signup failed", "invalid_credentials": "Invalid {field}", "invalid_content": "Invalid content", "not_verified": "{entity} has not been verified", "verify_token_fail": "Could not verify token", "recover_password_fail": "Could not send reset password link", "reset_password_fail": "Could not reset password", "token_expired": "<PERSON><PERSON> has expired. Please try again", "recaptcha_failed": "<PERSON><PERSON><PERSON><PERSON> failed", "register_fail": "{fieldOne} or {fieldTwo} is not registered to an account", "account_inactive": "Account is inactive", "password_incorrect": "Password is incorrect. {attemptsRemaining} attempt(s) remaining.", "password_mismatch": "Password mismatch", "already_verified": "{entity} is already verified", "configuration_not_found": "{party} configuration not found", "otp_expired": "OTP has expired", "otp_send_fail": "Could not send OTP", "otp_verify_fail": "Could not verify OTP", "profile_suspended": "Profile is suspended", "profile_banned": "Profile is banned", "profile_deactivated": "Profile is deactivated", "invalid_account_type": "Invalid account type", "validation_error": "{entity} validation failed", "seed_error": "{entity} seed failed", "seed_process": "Seed process failed"}, "success": {"created": "{entity} created successfully", "retrieved": "{entity} retrieved successfully", "updated": "{entity} updated successfully", "deleted": "{entity} deleted successfully", "activated": "{entity} activated successfully", "deactivated": "{entity} deactivated successfully", "ability": "You can delete {entity}", "login": "Login successful", "sign_up": "Sign Up successful. Please verify your account with the OTP sent to your mail.", "reset_password": "Password reset successfully", "account_verified": "Account verified successfully", "verification_otp_resent": "Verification OTP resent successfully", "password_reset_otp_sent": "Password reset OTP sent successfully", "seeded": "{entity} seeded successfully", "seed_process": "Seed process successful"}}