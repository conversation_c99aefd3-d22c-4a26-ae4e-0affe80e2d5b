import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CreateAddressDto {
  @AutoMap()
  @ApiProperty({
    name: 'latitude',
    type: String,
    required: true,
    description: 'The latitude of the address',
  })
  @IsString()
  latitude: string;

  @AutoMap()
  @ApiProperty({
    name: 'longitude',
    type: String,
    required: true,
    description: 'The longitude of the address',
  })
  @IsString()
  longitude: string;

  @AutoMap()
  @ApiProperty({
    name: 'houseNumber',
    type: String,
    required: false,
    description: 'The house number of the user',
  })
  houseNumber: number;

  @AutoMap()
  @ApiProperty({
    name:'street',
    type: String,
    required: false,
    description: 'The street of the user'
  })
  @IsString()
  street: string;

  @AutoMap()
  @ApiProperty({
    name:'city',
    type: String,
    required: false,
    description: 'The city of the user'
  })
  @IsString()
  city: string;

  @AutoMap()
  @ApiProperty({
    name: 'country',
    type: String,
    required: false,
    description: 'The country of the address',
  })
  @IsString()
  country: string;

  @AutoMap()
  @ApiProperty({
    name: 'state',
    type: String,
    required: true,
    description: 'The state of the address',
  })
  @IsString()
  state: string;

  @AutoMap()
  @ApiProperty({
    name: 'postalCode',
    type: String,
    required: false,
    description: 'The postal code of the address',
  })
  @IsString()
  postalCode: string;
}
