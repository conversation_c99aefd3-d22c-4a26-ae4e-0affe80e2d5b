import { Mapper, MappingProfile, createMap } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { AddressDto } from './dto/address.dto';
import { CreateAddressDto } from './dto/create-address.dto';
import { Address } from './entities/address.entity';

@Injectable()
export class AddressMapperService extends AutomapperProfile {
  constructor(
      @InjectMapper() mapper: Mapper,
      private readonly logger: LoggerService,
    ) {
      super(mapper);
      this.logger.setContext(AddressMapperService.name);
    }
  
    override get profile(): MappingProfile {
      return (mapper) => {
        createMap(mapper, Address, AddressDto);
        createMap(mapper, AddressDto, Address);
        createMap(mapper, CreateAddressDto, Address);
      };
    }
}
