import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';
import { PaginationQueryParams } from '@common/types/pagination';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { paginate } from 'nestjs-typeorm-paginate';
import { EntityManager, FindManyOptions, ILike, Repository } from 'typeorm';
import { CustomerValidationService } from './customer.validation.service';
import { Customer } from './entities/customer.entity';

@Injectable()
export class CustomerService implements EntityServiceStrategy<Customer> {
  constructor(
    @InjectRepository(Customer) private readonly customerRepository: Repository<Customer>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly customerValidator: CustomerValidationService,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(CustomerService.name);
  }

  async create(data: Customer): Promise<Customer> {
    return await this.customerRepository.save(data);
  }

  async modify(id: number, data: Customer): Promise<Customer> {
    await this.customerValidator.validate(data, DatabaseAction.UPDATE);
    return await this.customerRepository.save(data);
  }

  async findByPk(id: number): Promise<Customer> {
    return await this.customerRepository.findOneBy({ id });
  }

  async findAllCustomers(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      // Search across profile fields (firstName, lastName, email)
      where['profile'] = [{ firstName: ILike(`%${search}%`) }, { lastName: ILike(`%${search}%`) }, { email: ILike(`%${search}%`) }];
    }
    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit,
      route,
    };

    const queryOptions: FindManyOptions<Customer> = { where, order: { createdAt: 'desc'  } };

    const { items, meta, links } = await paginate(this.customerRepository, options, queryOptions);

    return { items, meta, links };
  }

  async findOneCustomer(id: number): Promise<Customer> {
    const customer = await this.findByPk(id);
    if (!customer) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: `Customer with id ${id}` } }));
    }
    return customer;
  }

  async findCustomerByProfileId(id: number): Promise<Customer> {
    const customer = await this.customerRepository.findOneBy({ profile: { id } });
    if (!customer) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: `Customer with profile id ${id}` } }));
    }
    return customer;
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const customer = await this.findOneCustomer(id);
        customer.status = EntityStatus.ACTIVE;
        customer.profile.status = EntityStatus.ACTIVE;
        customer.profile.profileStatus = ProfileStatus.ACTIVE;
        await this.customerRepository.save(customer);
      }),
    );
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const customer = await this.findOneCustomer(id);
        customer.status = EntityStatus.INACTIVE;
        customer.profile.status = EntityStatus.INACTIVE;
        customer.profile.profileStatus = ProfileStatus.INACTIVE;
        await this.customerRepository.save(customer);
      }),
    );
  }
}
