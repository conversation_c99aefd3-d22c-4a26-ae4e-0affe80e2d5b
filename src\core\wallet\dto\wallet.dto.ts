import { AutoMap } from '@automapper/classes';
import { EntityDto } from '@common/dto/base.dto';
import { Currency } from '@common/enumerations/currency.enum';
import { ApiProperty } from '@nestjs/swagger';

export class WalletDto extends EntityDto {
  @ApiProperty({
    description: 'Currency of the wallet',
    enum: Currency,
    name: 'currency',
    example: Currency.NGN,
  })
  @AutoMap()
  currency: Currency;

  @ApiProperty({
    description: 'Amount in the wallet',
    type: Number,
    name: 'amount',
    example: 1000,
  })
  @AutoMap()
  amount: number;
}
