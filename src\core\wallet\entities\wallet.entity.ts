import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';
import { Currency } from '@common/enumerations/currency.enum';
import { Column, Entity } from 'typeorm';

@Entity({ name: 'wallet' })
export class Wallet extends AbstractEntity {
  @AutoMap()
  @Column({
    name: 'currency',
    type: 'enum',
    enum: Currency,
  })
  currency: Currency;

  @AutoMap()
  @Column({ name: 'amount', type: 'decimal', precision: 10, scale: 2 })
  amount: number;
}
